<script lang="ts">
  import { onMount } from "svelte";

  // import Welcome from "../pages/welcome.svelte";
  import Intro from "./welcome-intro/+page.svelte";
  import UserIntro from "./user-intro/+page.svelte";
  import ps from "../stores/persistantStorage";
  import { invoke } from "@tauri-apps/api/core";
  import { getPrimaryWallet } from "../utils/wallet/getPrimaryWallet";

  alert("ALERT Script is executing - this should show in browser console")

  // Try different console methods
  console.log("TEST 1: Basic console.log");
  console.warn("TEST 2: Console warning");
  console.error("TEST 3: Console error");
  console.info("TEST 4: Console info");

  // Try console.table for visibility
  console.table([{test: "Console table test", working: true}]);

  // Try console.group for organization
  console.group("Console Group Test");
  console.log("This is inside a console group");
  console.groupEnd();

  console.log("app start.........................................................................");
  console.log("Script is executing - this should show in browser console");

  // Alternative debugging - add text to page
  if (typeof document !== 'undefined') {
    const debugDiv = document.createElement('div');
    debugDiv.style.cssText = 'position: fixed; top: 0; left: 0; background: red; color: white; padding: 10px; z-index: 9999;';
    debugDiv.textContent = 'DEBUG: Script is executing at ' + new Date().toISOString();
    document.body?.appendChild(debugDiv);
  }

  let name = $state("");
  let greetMsg = $state("");
  let statusMessage = $state("");
  let isLoading = $state(false);

  let hasUserCompletedIntro: Boolean | undefined = $state(undefined);
  let loading = $state(true);

  async function checkIfUserIsNew() {
    let wasUserNew = false;
    try {
      console.log("checking if user is new")
      hasUserCompletedIntro = await ps.getUserCompletedIntro();
      if (hasUserCompletedIntro === undefined || hasUserCompletedIntro === false) {
        await ps.initStore();
        console.log("user is new")
        hasUserCompletedIntro = false;
        wasUserNew = true;
      } else if (hasUserCompletedIntro) {
        wasUserNew = false;
        // window.location.href = '/screens/search';
      }
    } catch (e) {
      console.log(e)
    } finally {
      loading = false;
      return wasUserNew;
    }
  }

  async function initializeAutonomiClient() {
    // const walletKey = prompt("Enter wallet key:");
    // if (!walletKey) {
    //   statusMessage = "Wallet key is required";
    //   return;
    // }

    // const walletKey = "0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80";
    // const walletAddress = "******************************************";
    // const walletKey = "0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80";

    isLoading = true;
    statusMessage = "Initializing Autonomi client...";

    try {
      const primaryWallet = await getPrimaryWallet();
      const walletKey = primaryWallet?.privateKey
      console.log("primaryWallet", primaryWallet)
      const result = await invoke("initialize_autonomi_client", { walletKey });
      statusMessage = `Success: ${result}`;
    } catch (error) {
      statusMessage = `Error: ${error}`;
    } finally {
      isLoading = false;
    }
    console.log(statusMessage)
  }
  
  async function initPodManager(wasUserNew: boolean) {
    try {
      await invoke("initialize_datastore");
      // if (!wasUserNew) {await invoke("open_keystore", { password: "" });}
      await invoke("initialize_graph");
      // const result = await invoke("initialize_pod_manager");/

    } catch (error) {
      console.log(error);
    }
  }

  onMount(async () => {
    console.error("ERROR: onMount is executing");
    console.warn("WARNING: onMount is executing");
    console.log("onMount is executing - this should show in browser console");
    console.log("here maxx");
    const wasUserNew = await checkIfUserIsNew();
    console.log("wasUserNew:", wasUserNew);
    // await initializeAutonomiClient();
    // await initPodManager(wasUserNew);
    if (!wasUserNew) {
      console.log("User is not new, redirecting to search page");
      window.location.href = '/screens/search';
    } else {
      console.log("User is new, staying on intro page");
    }
  })

</script>

<main>
  <div class="">
    {#if !loading && hasUserCompletedIntro === false}
      <div class="">
        <!-- <Intro/> -->
         <UserIntro/>
      </div>
    {/if}
  </div>
</main>

